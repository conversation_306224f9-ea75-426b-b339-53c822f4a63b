package com.ascent.solidus.services.tenant;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;

/**
 * Service for loading and managing feature configurations from JSON files
 * This service initializes the system by reading JSON configurations
 */
@Service
public class FeatureConfigurationService {
    
    private static final Logger logger = LoggerFactory.getLogger(FeatureConfigurationService.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Autowired
    private TenantFeatureService tenantFeatureService;
    
    @Autowired
    private RoleFeaturePermissionService roleFeaturePermissionService;
    
    /**
     * Initialize configurations on startup
     */
    @PostConstruct
    public void initializeConfigurations() {
        logger.info("Initializing feature configurations from JSON files...");
        
        try {
            validateTenantFeaturesConfig();
            validateRolePermissionsConfig();
            validateTenantModuleConfig();
            
            logger.info("Feature configurations initialized successfully");
        } catch (Exception e) {
            logger.error("Error initializing feature configurations: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Validate tenant-features.json configuration
     */
    private void validateTenantFeaturesConfig() {
        try {
            ClassPathResource resource = new ClassPathResource("json/tenant-features.json");
            if (!resource.exists()) {
                logger.warn("tenant-features.json not found, creating default configuration");
                return;
            }
            
            JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
            
            // Validate structure
            if (!rootNode.has("feature_definitions")) {
                throw new IllegalStateException("tenant-features.json missing 'feature_definitions' section");
            }
            
            if (!rootNode.has("tenant_features")) {
                throw new IllegalStateException("tenant-features.json missing 'tenant_features' section");
            }
            
            // Count features and tenants
            JsonNode featureDefinitions = rootNode.get("feature_definitions");
            JsonNode tenantFeatures = rootNode.get("tenant_features");
            
            int featureCount = featureDefinitions.size();
            int tenantCount = tenantFeatures.size();
            
            logger.info("Loaded {} feature definitions and {} tenant configurations", featureCount, tenantCount);
            
        } catch (IOException e) {
            logger.error("Error validating tenant-features.json: {}", e.getMessage());
            throw new RuntimeException("Failed to validate tenant features configuration", e);
        }
    }
    
    /**
     * Validate role-feature-permissions.json configuration
     */
    private void validateRolePermissionsConfig() {
        try {
            ClassPathResource resource = new ClassPathResource("json/role-feature-permissions.json");
            if (!resource.exists()) {
                logger.warn("role-feature-permissions.json not found, creating default configuration");
                return;
            }
            
            JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
            
            // Validate structure
            if (!rootNode.has("role_feature_permissions")) {
                throw new IllegalStateException("role-feature-permissions.json missing 'role_feature_permissions' section");
            }
            
            JsonNode rolePermissions = rootNode.get("role_feature_permissions");
            int roleCount = rolePermissions.size();
            
            logger.info("Loaded permissions for {} roles", roleCount);
            
            // Validate each role has proper structure
            rolePermissions.fieldNames().forEachRemaining(roleName -> {
                JsonNode role = rolePermissions.get(roleName);
                if (!role.has("accessible_features")) {
                    logger.warn("Role {} missing 'accessible_features' section", roleName);
                }
            });
            
        } catch (IOException e) {
            logger.error("Error validating role-feature-permissions.json: {}", e.getMessage());
            throw new RuntimeException("Failed to validate role permissions configuration", e);
        }
    }
    
    /**
     * Validate tenant-module-configs.json configuration
     */
    private void validateTenantModuleConfig() {
        try {
            ClassPathResource resource = new ClassPathResource("json/tenant-module-configs.json");
            if (!resource.exists()) {
                logger.warn("tenant-module-configs.json not found, skipping validation");
                return;
            }
            
            JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
            
            // Validate structure
            if (!rootNode.has("tenant_module_configurations")) {
                throw new IllegalStateException("tenant-module-configs.json missing 'tenant_module_configurations' section");
            }
            
            JsonNode tenantConfigs = rootNode.get("tenant_module_configurations");
            int configCount = tenantConfigs.size();
            
            logger.info("Loaded module configurations for {} tenants", configCount);
            
        } catch (IOException e) {
            logger.error("Error validating tenant-module-configs.json: {}", e.getMessage());
            throw new RuntimeException("Failed to validate tenant module configuration", e);
        }
    }
    
    /**
     * Reload configurations (for runtime updates)
     */
    public void reloadConfigurations() {
        logger.info("Reloading feature configurations...");
        initializeConfigurations();
    }
    
    /**
     * Get configuration summary
     */
    public ConfigurationSummary getConfigurationSummary() {
        ConfigurationSummary summary = new ConfigurationSummary();
        
        try {
            // Count features
            ClassPathResource tenantResource = new ClassPathResource("json/tenant-features.json");
            if (tenantResource.exists()) {
                JsonNode rootNode = objectMapper.readTree(tenantResource.getInputStream());
                JsonNode featureDefinitions = rootNode.get("feature_definitions");
                JsonNode tenantFeatures = rootNode.get("tenant_features");
                
                summary.setFeatureCount(featureDefinitions != null ? featureDefinitions.size() : 0);
                summary.setTenantCount(tenantFeatures != null ? tenantFeatures.size() : 0);
            }
            
            // Count roles
            ClassPathResource roleResource = new ClassPathResource("json/role-feature-permissions.json");
            if (roleResource.exists()) {
                JsonNode rootNode = objectMapper.readTree(roleResource.getInputStream());
                JsonNode rolePermissions = rootNode.get("role_feature_permissions");
                
                summary.setRoleCount(rolePermissions != null ? rolePermissions.size() : 0);
            }
            
        } catch (Exception e) {
            logger.error("Error getting configuration summary: {}", e.getMessage());
        }
        
        return summary;
    }
    
    /**
     * Configuration summary DTO
     */
    public static class ConfigurationSummary {
        private int featureCount;
        private int tenantCount;
        private int roleCount;
        private String status = "OK";
        
        // Getters and setters
        public int getFeatureCount() { return featureCount; }
        public void setFeatureCount(int featureCount) { this.featureCount = featureCount; }
        
        public int getTenantCount() { return tenantCount; }
        public void setTenantCount(int tenantCount) { this.tenantCount = tenantCount; }
        
        public int getRoleCount() { return roleCount; }
        public void setRoleCount(int roleCount) { this.roleCount = roleCount; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }
}
