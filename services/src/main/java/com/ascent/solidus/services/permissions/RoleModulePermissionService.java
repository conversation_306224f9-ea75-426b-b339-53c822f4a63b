package com.ascent.solidus.services.permissions;

import com.ascent.solidus.core.constants.RoleName;
import com.ascent.solidus.core.domain.permissions.RoleModulePermission;
import com.ascent.solidus.core.repository.permissions.RoleModulePermissionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service for managing role-module permissions
 * This handles the role-based access control shown in the UI
 */
@Service
public class RoleModulePermissionService {
    
    private static final Logger logger = LoggerFactory.getLogger(RoleModulePermissionService.class);
    
    @Autowired
    private RoleModulePermissionRepository roleModulePermissionRepository;
    
    /**
     * Get all permissions for a role (for UI display)
     */
    @Transactional(readOnly = true)
    public List<RoleModulePermission> getRolePermissions(RoleName roleName) {
        return roleModulePermissionRepository.findByRoleNameAndActiveTrue(roleName);
    }
    
    /**
     * Get role permissions organized by parent modules (for UI tree structure)
     */
    @Transactional(readOnly = true)
    public Map<String, List<RoleModulePermission>> getRolePermissionsGrouped(RoleName roleName) {
        List<RoleModulePermission> permissions = getRolePermissions(roleName);
        
        return permissions.stream()
                .collect(Collectors.groupingBy(
                    perm -> perm.isParentModule() ? perm.getModuleName() : perm.getParentModuleName()
                ));
    }
    
    /**
     * Check if role has access to a module
     */
    @Transactional(readOnly = true)
    public boolean hasRoleAccessToModule(RoleName roleName, String moduleName) {
        return roleModulePermissionRepository.hasRoleAccessToModule(roleName, moduleName);
    }
    
    /**
     * Check if role has specific permission for a module
     */
    @Transactional(readOnly = true)
    public boolean hasRolePermission(RoleName roleName, String moduleName, String permissionType) {
        Optional<RoleModulePermission> permission = 
                roleModulePermissionRepository.findRoleModulePermission(roleName, moduleName);
        
        if (permission.isEmpty()) {
            return false;
        }
        
        RoleModulePermission perm = permission.get();
        switch (permissionType.toUpperCase()) {
            case "READ":
                return perm.isCanRead();
            case "WRITE":
                return perm.isCanWrite();
            case "DELETE":
                return perm.isCanDelete();
            case "EXECUTE":
                return perm.isCanExecute();
            default:
                return false;
        }
    }
    
    /**
     * Assign modules to a role (like the Save button in UI)
     */
    @Transactional
    public void assignModulesToRole(RoleName roleName, List<ModulePermissionRequest> moduleRequests) {
        logger.info("Assigning modules to role: {}", roleName);
        
        // First, deactivate all existing permissions for this role
        List<RoleModulePermission> existingPermissions = 
                roleModulePermissionRepository.findByRoleNameAndActiveTrue(roleName);
        
        for (RoleModulePermission existing : existingPermissions) {
            existing.setActive(false);
        }
        roleModulePermissionRepository.saveAll(existingPermissions);
        
        // Then create new permissions
        for (ModulePermissionRequest request : moduleRequests) {
            RoleModulePermission permission = new RoleModulePermission(
                    roleName, 
                    request.getModuleName(), 
                    request.getParentModuleName(), 
                    request.isParentModule()
            );
            
            permission.setCanRead(request.isCanRead());
            permission.setCanWrite(request.isCanWrite());
            permission.setCanDelete(request.isCanDelete());
            permission.setCanExecute(request.isCanExecute());
            
            roleModulePermissionRepository.save(permission);
        }
        
        logger.info("Successfully assigned {} modules to role {}", moduleRequests.size(), roleName);
    }
    
    /**
     * Get all accessible modules for a role
     */
    @Transactional(readOnly = true)
    public List<String> getAccessibleModules(RoleName roleName) {
        return roleModulePermissionRepository.getAccessibleModulesForRole(roleName);
    }
    
    /**
     * Get accessible parent modules for a role
     */
    @Transactional(readOnly = true)
    public List<String> getAccessibleParentModules(RoleName roleName) {
        return roleModulePermissionRepository.getAccessibleParentModulesForRole(roleName);
    }
    
    /**
     * Get accessible sub-modules for a role and parent module
     */
    @Transactional(readOnly = true)
    public List<String> getAccessibleSubModules(RoleName roleName, String parentModuleName) {
        return roleModulePermissionRepository.getAccessibleSubModulesForRole(roleName, parentModuleName);
    }
    
    /**
     * Check if user (with multiple roles) has access to a module
     */
    @Transactional(readOnly = true)
    public boolean hasUserAccessToModule(List<RoleName> userRoles, String moduleName) {
        return userRoles.stream()
                .anyMatch(role -> hasRoleAccessToModule(role, moduleName));
    }
    
    /**
     * Check if user has specific permission for a module
     */
    @Transactional(readOnly = true)
    public boolean hasUserPermissionForModule(List<RoleName> userRoles, String moduleName, String permissionType) {
        return userRoles.stream()
                .anyMatch(role -> hasRolePermission(role, moduleName, permissionType));
    }
    
    /**
     * DTO for module permission requests
     */
    public static class ModulePermissionRequest {
        private String moduleName;
        private String parentModuleName;
        private boolean isParentModule;
        private boolean canRead = true;
        private boolean canWrite = false;
        private boolean canDelete = false;
        private boolean canExecute = false;
        
        // Constructors
        public ModulePermissionRequest() {}
        
        public ModulePermissionRequest(String moduleName, String parentModuleName, boolean isParentModule) {
            this.moduleName = moduleName;
            this.parentModuleName = parentModuleName;
            this.isParentModule = isParentModule;
        }
        
        // Getters and Setters
        public String getModuleName() { return moduleName; }
        public void setModuleName(String moduleName) { this.moduleName = moduleName; }
        
        public String getParentModuleName() { return parentModuleName; }
        public void setParentModuleName(String parentModuleName) { this.parentModuleName = parentModuleName; }
        
        public boolean isParentModule() { return isParentModule; }
        public void setParentModule(boolean parentModule) { isParentModule = parentModule; }
        
        public boolean isCanRead() { return canRead; }
        public void setCanRead(boolean canRead) { this.canRead = canRead; }
        
        public boolean isCanWrite() { return canWrite; }
        public void setCanWrite(boolean canWrite) { this.canWrite = canWrite; }
        
        public boolean isCanDelete() { return canDelete; }
        public void setCanDelete(boolean canDelete) { this.canDelete = canDelete; }
        
        public boolean isCanExecute() { return canExecute; }
        public void setCanExecute(boolean canExecute) { this.canExecute = canExecute; }
    }
}
