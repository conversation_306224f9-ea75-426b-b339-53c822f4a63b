package com.ascent.solidus.core.repository.permissions;

import com.ascent.solidus.core.constants.RoleName;
import com.ascent.solidus.core.domain.permissions.RoleModulePermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RoleModulePermissionRepository extends JpaRepository<RoleModulePermission, Long> {
    
    /**
     * Find all permissions for a specific role
     */
    List<RoleModulePermission> findByRoleNameAndActiveTrue(RoleName roleName);
    
    /**
     * Find permission for a specific role and module
     */
    Optional<RoleModulePermission> findByRoleNameAndModuleNameAndActiveTrue(RoleName roleName, String moduleName);
    
    /**
     * Find all parent module permissions for a role
     */
    List<RoleModulePermission> findByRoleNameAndIsParentModuleTrueAndActiveTrue(RoleName roleName);
    
    /**
     * Find all sub-module permissions for a role and parent module
     */
    List<RoleModulePermission> findByRoleNameAndParentModuleNameAndIsParentModuleFalseAndActiveTrue(
            RoleName roleName, String parentModuleName);
    
    /**
     * Check if role has access to a specific module
     */
    @Query("SELECT COUNT(rmp) > 0 FROM RoleModulePermission rmp " +
           "WHERE rmp.roleName = :roleName AND rmp.moduleName = :moduleName AND rmp.active = true")
    boolean hasRoleAccessToModule(@Param("roleName") RoleName roleName, @Param("moduleName") String moduleName);
    
    /**
     * Get all accessible modules for a role
     */
    @Query("SELECT rmp.moduleName FROM RoleModulePermission rmp " +
           "WHERE rmp.roleName = :roleName AND rmp.active = true")
    List<String> getAccessibleModulesForRole(@Param("roleName") RoleName roleName);
    
    /**
     * Get all accessible parent modules for a role
     */
    @Query("SELECT rmp.moduleName FROM RoleModulePermission rmp " +
           "WHERE rmp.roleName = :roleName AND rmp.isParentModule = true AND rmp.active = true")
    List<String> getAccessibleParentModulesForRole(@Param("roleName") RoleName roleName);
    
    /**
     * Get all accessible sub-modules for a role and parent module
     */
    @Query("SELECT rmp.moduleName FROM RoleModulePermission rmp " +
           "WHERE rmp.roleName = :roleName AND rmp.parentModuleName = :parentModuleName " +
           "AND rmp.isParentModule = false AND rmp.active = true")
    List<String> getAccessibleSubModulesForRole(@Param("roleName") RoleName roleName, 
                                               @Param("parentModuleName") String parentModuleName);
    
    /**
     * Delete all permissions for a role (for reassignment)
     */
    void deleteByRoleNameAndActiveTrue(RoleName roleName);
    
    /**
     * Check if role has specific permission type for a module
     */
    @Query("SELECT rmp FROM RoleModulePermission rmp " +
           "WHERE rmp.roleName = :roleName AND rmp.moduleName = :moduleName AND rmp.active = true")
    Optional<RoleModulePermission> findRoleModulePermission(@Param("roleName") RoleName roleName, 
                                                           @Param("moduleName") String moduleName);
}
