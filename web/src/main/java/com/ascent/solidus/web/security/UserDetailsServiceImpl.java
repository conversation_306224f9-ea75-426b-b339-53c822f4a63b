package com.ascent.solidus.web.security;

import com.ascent.solidus.core.domain.module.AppUser;
import com.ascent.solidus.core.repository.umgmt.AppUserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ascent.solidus.core.domain.umgmt.RoleAssignment;
import com.ascent.solidus.core.repository.umgmt.RoleAssignmentRepository;
import com.ascent.solidus.core.security.UserPrincipal;



import java.util.List;
import java.util.stream.Collectors;

@Service
@Primary
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    AppUserRepository userRepository;

    @Autowired
    RoleAssignmentRepository roleAssignmentRepository;

    @Override
    @Transactional
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        try {
//            log.debug("Attempting to load user by username: {}", username);
            
            AppUser user = userRepository.findByUsernameAndActive(username, true)
                    .orElseThrow(() -> new UsernameNotFoundException("User Not Found with username: " + username));

            // Get role assignments for this user
            List<RoleAssignment> roleAssignments = roleAssignmentRepository.findByAppUserAndActive(user, true);
//            log.debug("Found {} role assignments for user {}", roleAssignments.size(), username);
            
            // Convert role assignments to authorities
            List<SimpleGrantedAuthority> authorities = roleAssignments.stream()
                    .map(ra -> new SimpleGrantedAuthority(ra.getRole().getName().name()))
                    .collect(Collectors.toList());

            // Get organization ID
            Long organizationId = user.getOrganization() != null ? user.getOrganization().getId() : null;
            
            // Get factory ID
            String factoryId = user.getFactory() != null ? user.getFactory().getId().toString() : null;

            return UserPrincipal.builder()
                    .id(user.getId())
                    .username(user.getUsername())
                    .email(user.getEmail())
                    .password(user.getPassword())
                    .firstName(user.getFirstName())
                    .lastName(user.getLastName())
                    .tenantId(user.getTenant().getId())
                    .organizationId(organizationId)
                    .factoryId(factoryId)
                    .enabled(user.isEnabled())
                    .accountNonLocked(user.isAccountNonLocked())
                    .authorities(authorities)
                    .build();
        } catch (Exception e) {
//            log.error("Error loading user by username: {}", username, e);
            throw e;
        }
    }
} 