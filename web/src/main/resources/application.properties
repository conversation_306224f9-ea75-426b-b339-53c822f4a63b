spring.application.name=web

# PostgreSQL Database Configuration
spring.datasource.url=*****************************************
spring.datasource.username=neha<PERSON>ukala
#spring.datasource.password=password
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.id.new_generator_mappings=true

# Server Configuration
server.port=8080
#okta.oauth2.client-id=0oapjci239reTLA0m5d7
#okta.oauth2.client-secret=Dv_ET7qYV5L60AORDnMBNTESAariwZuAZ9sZIivCE-Xir-dvTMYvL0RAhuZVq97B
#okta.oauth2.issuer=https://dev-40977251.okta.com/oauth2/default

#spring.security.oauth2.client.registration.okta.client-id=0oapjci239reTLA0m5d7
#spring.security.oauth2.client.registration.okta.client-secret=Dv_ET7qYV5L60AORDnMBNTESAariwZuAZ9sZIivCE-Xir-dvTMYvL0RAhuZVq97B
#spring.security.oauth2.client.registration.okta.scope=openid, profile, email
#spring.security.oauth2.client.registration.okta.authorization-grant-type=authorization_code
##spring.security.oauth2.client.registration.okta.redirect-uri=http://localhost:8080/login

#spring.security.oauth2.client.provider.okta.issuer-uri=https://dev-40977251.okta.com/oauth2/default

spring.security.oauth2.client.registration.azure.provider=azure

spring.security.oauth2.client.registration.azure.client-id=d5b735e7-beb1-4468-abe7-530c851fb670
spring.security.oauth2.client.registration.azure.client-secret=****************************************
spring.security.oauth2.client.registration.azure.scope=openid
spring.security.oauth2.client.registration.azure.redirect-uri=http://localhost:8080/login/oauth2/code/
spring.security.oauth2.client.provider.azure.issuer-uri=https://login.microsoftonline.com/a644ddd0-ada1-4a18-aa1d-21caad21e4be/v2.0

# JWT Configuration
jwt.secret=your-256-bit-secret-key-here-make-it-long-and-secure-in-production
jwt.expiration=3600000

# Allow bean definition overriding
spring.main.allow-bean-definition-overriding=true

# Enable debug logging for Spring Boot
logging.level.org.springframework=INFO
logging.level.com.ascent.solidus=DEBUG

# Enable debug logging for Spring Security
logging.level.org.springframework.security=DEBUG
logging.level.com.ascent.solidus.web.security=DEBUG

# Swagger/OpenAPI Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.filter=true
